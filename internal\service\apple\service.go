package apple

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/golang-jwt/jwt"
	"github.com/lestrrat-go/jwx/v2/jwk"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// Auth
	HandleAppleCallback(access string) (*AppleUserDetails, error)
	Register(appleUserDetails AppleUserDetails, onboarding *model.Onboarding, photo *multipart.FileHeader, photoURL string, referralCode string) (*token.Token, error)
	Login(appleUserDetails AppleUserDetails) (*model.User, *token.Token, error)
}

type service struct {
	ClientID       string
	TeamID         string
	KeyID          string
	PrivateKey     string
	UserService    user.Service
	BillingService billing.Service
	S3Service      s3.Service
}

func New(userService user.Service, billingService billing.Service, s3Service s3.Service) Service {
	return &service{
		ClientID:       os.Getenv("APPLE_CLIENT_ID"),
		TeamID:         os.Getenv("APPLE_TEAM_ID"),
		KeyID:          os.Getenv("APPLE_KEY_ID"),
		PrivateKey:     os.Getenv("APPLE_PRIVATE_KEY"),
		UserService:    userService,
		BillingService: billingService,
		S3Service:      s3Service,
	}
}

// AppleUserDetails struct
type AppleUserDetails struct {
	ID             string
	Name           string
	LastName       string
	Email          string
	EmailVerified  bool
	IsPrivateEmail bool
	RegisterSource string
}

// Auth
func (s *service) HandleAppleCallback(access string) (*AppleUserDetails, error) {
	// Trim was needed since it was receiving the string with "" (quotes)
	claims, err := s.verifyAndParseAppleIDToken(strings.Trim(access, "\""))
	if err != nil {
		return nil, err
	}

	uniqueID, _ := claims["sub"].(string)
	email, _ := claims["email"].(string)
	emailVerified, _ := claims["email_verified"].(bool)
	isPrivateEmail, _ := claims["is_private_email"].(bool)

	userDetails := &AppleUserDetails{
		ID:             uniqueID,
		Email:          email,
		EmailVerified:  emailVerified,
		IsPrivateEmail: isPrivateEmail,
	}

	return userDetails, nil
}

// Register registers a new user using Apple user details
func (s *service) Register(appleUserDetails AppleUserDetails, onboarding *model.Onboarding, photo *multipart.FileHeader, photoURL string, referralCode string) (*token.Token, error) {
	user := &model.User{
		Name:     appleUserDetails.Name, // Apple doesn't provide a name, use a placeholder or fetch name separately
		LastName: appleUserDetails.LastName,
		Email:    appleUserDetails.Email,
		Password: RandomOAuthStateString(10) + "@" + RandomOAuthStateString(10),
		//PhotoURL:   "", // Apple doesn't provide an avatar, you may set a default avatar
		Onboarding:     onboarding,
		RegisterSource: appleUserDetails.RegisterSource,
	}

	// If a custom photo is provided, handle photo upload
	if photoURL != "" {
		user.PhotoURL = photoURL
	} else if photo != nil {
		uploadedPhotoURL, err := s.S3Service.UploadFile(context.Background(), photo, os.Getenv("AWS_S3_USER_PHOTOS_FOLDER"))
		if err != nil {
			return nil, err
		}
		user.PhotoURL = uploadedPhotoURL
	}

	if err := user.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.UserService.Create(context.TODO(), user, referralCode); err != nil {
		return nil, err
	}

	createdUser, err := s.UserService.FindByEmail(context.TODO(), user.Email)
	if err != nil {
		return nil, err
	}

	// Link any existing subscriptions to the newly created user
	userObjectID, err := primitive.ObjectIDFromHex(createdUser.ID)
	if err == nil {
		// Don't fail registration if subscription linking fails, just log it
		if err := s.BillingService.LinkExistingSubscriptionsToUser(context.TODO(), userObjectID, createdUser.Email); err != nil {
			// Log error but continue with registration
			// TODO: Consider using structured logging here
		}
	}

	token, err := token.Create(createdUser)
	if err != nil {
		return nil, err
	}

	return token, nil
}

// Login logs in an existing user using Apple user details
func (s *service) Login(appleUserDetails AppleUserDetails) (*model.User, *token.Token, error) {
	user, err := s.UserService.FindByEmail(context.TODO(), appleUserDetails.Email)
	if err != nil {
		return nil, nil, err
	}

	// Log the user in.
	err = user.PrepareLogin()
	if err != nil {
		return nil, nil, err
	}

	token, err := token.Create(user)
	if err != nil {
		return nil, nil, err
	}

	return user, token, nil
}

// Helper
// RandomOAuthStateString will return a random string to increase security.
func RandomOAuthStateString(n int) string {
	data := make([]byte, n)
	if _, err := io.ReadFull(rand.Reader, data); err != nil {
		return ""
	}

	return base64.StdEncoding.EncodeToString(data)
}

// verifyAndParseAppleIDToken verifies the Apple ID token and parses its claims
func (s *service) verifyAndParseAppleIDToken(idToken string) (jwt.MapClaims, error) {
	// Fetch Apple's public keys
	resp, err := http.Get("https://appleid.apple.com/auth/keys")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Apple's public keys: %v", err)
	}
	defer resp.Body.Close()

	// Read the response body into a byte slice
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Parse the JWK set
	jwks, err := jwk.Parse(body)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Apple's public keys: %v", err)
	}

	// Parse the JWT token
	token, err := jwt.Parse(idToken, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		kid, ok := token.Header["kid"].(string)
		if !ok {
			return nil, fmt.Errorf("missing kid in token header")
		}

		key, found := jwks.LookupKeyID(kid)
		if !found {
			return nil, fmt.Errorf("unable to find key %v", kid)
		}

		var raw interface{}
		if err := key.Raw(&raw); err != nil {
			return nil, fmt.Errorf("failed to get raw key: %v", err)
		}
		return raw, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %v. Token: %s", err, idToken)
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}
