{"auth": {"error": {"invalidLoginInput": "Opa! Parece que faltou preencher algo. Dê uma olhadinha nos campos e tente de novo.", "loginValidationFailed": "Hmm, e-mail ou senha incorretos. Que tal tentar mais uma vez?", "invalidRegisterInput": "Opa! Parece que faltou preencher algo. Dê uma olhadinha em todos os campos para continuar.", "failedToParseFormData": "<PERSON><PERSON>, não conseguimos ler suas informações. Poderia tentar enviá-las de novo?", "invalidOboardingAgeRange": "Hmm, parece que há algo errado com a faixa etária. Pode verificar e tentar de novo?", "invalidOboardingFinancialSituation": "Opa, algo na sua situação financeira parece incorreto. Poderia verificar?", "invalidOboardingFinancialGoal": "Hmm, não entendemos sua meta financeira. Que tal revisar e tentar de novo?", "invalidOboardingPersonalInterest": "Parece que seus interesses não foram salvos. Vamos tentar mais uma vez!", "failedToProcessPhoto": "<PERSON>ão conseguimos carregar sua foto. Que tal tentar com outra imagem ou um pouco mais tarde?", "invalidFileType": "Tipo de arquivo não suportado! Por favor, envie uma imagem JPG, JPEG ou PNG.", "fileTooLarge": "Essa foto é muito grande! Tente uma imagem com no máximo 5MB.", "invalidRefreshTokenInput": "<PERSON><PERSON>, algo inesperado aconteceu. Por favor, tente novamente.", "invalidForgotPasswordInput": "Hmm, algo não deu certo. Verifique seus dados e tente pedir a recuperação de novo.", "invalidResetPasswordInput": "Opa! Algo deu errado ao redefinir sua senha. Vamos tentar mais uma vez.", "invalidCheckPasswordInput": "Essa senha não parece certa. Dê uma olhadinha e tente de novo.", "userNotLoggedIn": "Parece que você não está conectado. Faça o login para continuar sua jornada!", "invalidAdminLoginInput": "Hmm, os dados de login do administrador não parecem corretos. Verifique e tente de novo.", "": "", "failedToUploadPhoto": "Ops, não conseguimos enviar sua foto. Que tal tentar com outra imagem?", "failedToCreateToken": "<PERSON><PERSON>, algo deu errado do nosso lado. Por favor, tente novamente em um instante.", "failedToRetrieveUserAfterCreation": "Sua conta foi criada, mas não conseguimos te conectar agora. Que tal tentar fazer login?", "brevoNotifierNotAvailable": "Ops! Não conseguimos enviar o e-mail agora. Por favor, tente novamente em alguns minutos.", "userNotAdmin": "Opa! Parece que esta é uma área restrita. Apenas para os mestres do jogo!", "userNotHR": "Opa! Parece que esta é uma área restrita. Apenas para os RH!", "invalidHRLoginInput": "Hmm, os dados de login do RH não parecem corretos. Verifique e tente de novo."}}, "progression": {"error": {"conflict": "Você já tem progresso salvo! Continue de onde parou.", "createFailed": "Ops! Não conseguimos salvar seu progresso. Tente novamente.", "invalidIdFormat": "ID de progresso inválido. Verifique os dados e tente novamente.", "notFound": "Progresso não encontrado. Que tal começar uma nova jornada?", "findFailed": "Ops! Não conseguimos carregar seu progresso. Tente novamente.", "notFoundForUser": "Nenhum progresso encontrado. Comece sua jornada de aprendizado agora!", "findByUserFailed": "Ops! Não conseguimos carregar seu progresso. Tente novamente.", "updateConflict": "Não foi possível atualizar seu progresso. Tente novamente.", "updateFailed": "Ops! Não conseguimos salvar suas conquistas. Tente novamente.", "notFoundForUpdate": "Progresso não encontrado para atualização. Tente novamente.", "deleteFailed": "Ops! Não conseguimos resetar seu progresso. Tente novamente.", "notFoundForDeletion": "Progresso não encontrado para exclusão. Pode já ter sido removido.", "findTrailProgressionsFailed": "Ops! Não conseguimos carregar suas trilhas. Tente novamente."}}, "user": {"error": {"hashPassword": "<PERSON><PERSON>, tivemos um probleminha para salvar sua senha. Que tal tentar de novo?", "forbidden": "Opa! Parece que você precisa de uma chave especial para acessar aqui.", "invalidCredentials": "Hmm, essa combinação de e-mail e senha não parece certa. Vamos tentar de novo?", "resetPassword": "Opa! Algo deu errado ao tentar redefinir sua senha. Por favor, tente mais uma vez.", "mergeFailed": "<PERSON>h, tivemos um problema para atualizar suas informações. Poderia tentar novamente?", "processPassword": "<PERSON><PERSON>, tivemos um probleminha com a sua senha. Poderia tentar novamente?", "emailRequired": "<PERSON><PERSON>, faltou o e-mail! Por favor, preencha para continuar.", "invalidEmail": "Hmm, esse e-mail não parece válido. Que tal dar uma ol<PERSON><PERSON><PERSON>?", "emptyId": "Opa! Algo inesperado aconteceu do nosso lado. Por favor, tente novamente.", "nameRequired": "Quase lá! Só falta preencher seu nome.", "passwordRequired": "Não se esqueça da senha! Ela é super importante para proteger sua conta.", "referralCodeRequired": "Faltou o código de indicação! Preencha para continuar.", "setRoleNotAllowed": "Opa! Essa é uma ação superpoderosa que não pode ser feita por aqui.", "phoneRequired": "Opa, precisamos do seu telefone para continuar.", "passwordRequirements": "Para uma senha super segura, ela precisa ter:\n- <PERSON><PERSON> menos 6 caracteres\n- Uma letra mai<PERSON> (A-Z)\n- Uma letra minúscula (a-z)\n- <PERSON> (0-9)\n- Um caractere especial (!@#$)", "": "", "conflict": "Este usuário já existe. Tente com um email diferente!", "notFoundById": "Usuário não encontrado. Verifique se o ID está correto.", "notFoundByEmail": "Nenhum usuário encontrado com este email. Que tal criar uma conta?", "notFoundByReferral": "Código de indicação inválido. Verifique se digitou corretamente.", "deletedNotFoundByEmail": "Nenhuma conta excluída encontrada com este email.", "conflictUpdate": "Não foi possível atualizar. Este email já está sendo usado por outro usuário.", "notFoundForUpdate": "Usuário não encontrado para atualização. Tente novamente.", "notFoundForDeletion": "Usuário não encontrado para exclusão. Pode já ter sido removido.", "createFailed": "Ops! Algo deu errado ao criar sua conta. Tente novamente.", "deletedCreateFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "findByIdFailed": "Ops! Algo deu errado ao buscar o usuário. Tente novamente.", "findAllFailed": "Ops! Algo deu errado ao carregar os usuários. Tente novamente.", "decodeUserFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "adminUsersNotFound": "Ops! Algo deu errado ao buscar administradores. Tente novamente.", "accessDenied": "Ops! Acesso negado. Você não tem permissão para executar esta ação.", "decodeAdminUserFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "findByEmailFailed": "Ops! Algo deu errado ao buscar o usuário. Tente novamente.", "findByReferralFailed": "Ops! Algo deu errado ao verificar o código. Tente novamente.", "findByReferringUserIdFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "cursorError": "Ops! Algo deu errado. Tente novamente mais tarde.", "findWithFilterFailed": "Ops! Algo deu errado ao buscar usuários. Tente novamente.", "deletedFindByEmailFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "invalidId": "ID do usuário inválido. Verifique os dados e tente novamente.", "updateFailed": "Ops! Algo deu errado ao atualizar. Tente novamente.", "deleteFailed": "Ops! Algo deu errado ao excluir. Tente novamente.", "deletedConflictExists": "Esta conta já foi excluída anteriormente."}}}